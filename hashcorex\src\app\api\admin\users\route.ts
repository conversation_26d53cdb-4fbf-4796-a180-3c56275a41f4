import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

function getOrderBy(sortBy: string, sortOrder: string) {
  const order = sortOrder === 'asc' ? 'asc' : 'desc';

  switch (sortBy) {
    case 'createdAt':
      return { createdAt: order };
    case 'walletBalance':
      return { walletBalance: { availableBalance: order } };
    case 'activeTHS':
      // For activeTHS, we'll need to handle this in post-processing since it's calculated
      return { createdAt: 'desc' }; // Default fallback
    default:
      return { createdAt: 'desc' };
  }
}

export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);
    
    if (!authenticated || !user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || 'all';
    const sortBy = searchParams.get('sortBy') || '';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    
    if (search) {
      where.OR = [
        { email: { contains: search, mode: 'insensitive' } },
        { firstName: { contains: search, mode: 'insensitive' } },
        { lastName: { contains: search, mode: 'insensitive' } },
        { referralId: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (status !== 'all') {
      where.isActive = status === 'active';
    }

    // Get users with pagination and mining units for active TH/s calculation
    const [users, totalCount] = await Promise.all([
      prisma.user.findMany({
        where,
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          referralId: true,
          role: true,
          isActive: true,
          kycStatus: true,
          createdAt: true,
          walletBalance: {
            select: {
              availableBalance: true,
            },
          },
          miningUnits: {
            select: {
              thsAmount: true,
              status: true,
              expiryDate: true,
            },
          },
          kycDocuments: {
            select: {
              id: true,
              status: true,
            },
          },
          _count: {
            select: {
              miningUnits: true,
              transactions: true,
            },
          },
        },
        orderBy: getOrderBy(sortBy, sortOrder),
        skip,
        take: limit,
      }),
      prisma.user.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    // Calculate active TH/s and determine actual KYC status for each user
    let usersWithMiningPower = users.map(user => {
      const activeTHS = user.miningUnits.reduce((total, unit) => {
        const isActive = unit.status === 'ACTIVE' && unit.expiryDate > new Date();
        return total + (isActive ? unit.thsAmount : 0);
      }, 0);

      // Determine actual KYC status based on document submission
      let actualKycStatus = user.kycStatus;

      // If user has PENDING status but no KYC documents, show as "Not Submitted"
      if (user.kycStatus === 'PENDING' && user.kycDocuments.length === 0) {
        actualKycStatus = 'NOT_SUBMITTED';
      }

      // Remove miningUnits and kycDocuments from response to keep it clean
      const { miningUnits, kycDocuments, ...userWithoutMiningUnits } = user;

      return {
        ...userWithoutMiningUnits,
        kycStatus: actualKycStatus,
        activeTHS,
      };
    });

    // Handle activeTHS sorting (post-processing since it's calculated)
    if (sortBy === 'activeTHS') {
      usersWithMiningPower.sort((a, b) => {
        const comparison = a.activeTHS - b.activeTHS;
        return sortOrder === 'asc' ? comparison : -comparison;
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        users: usersWithMiningPower,
        pagination: {
          currentPage: page,
          totalPages,
          totalCount,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      },
    });

  } catch (error) {
    console.error('Admin users fetch error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}
