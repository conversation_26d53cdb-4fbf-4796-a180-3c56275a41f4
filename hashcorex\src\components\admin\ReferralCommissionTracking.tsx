'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON><PERSON>er, Card<PERSON><PERSON>le, CardContent, Input, Button } from '@/components/ui';
import {
  Users,
  Search,
  Filter,
  Download,
  Eye,
  TrendingUp,
  DollarSign,
  Calendar,
  ArrowRight,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { formatCurrency, formatNumber, formatDateTime } from '@/lib/utils';

interface ReferralCommission {
  id: string;
  fromUserId: string;
  toUserId: string;
  fromUser: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
  };
  toUser: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
  };
  amount: number;
  commissionRate: number;
  originalAmount: number;
  type: 'DIRECT_REFERRAL' | 'MINING_PURCHASE';
  description: string;
  createdAt: string;
  status: 'COMPLETED' | 'PENDING' | 'FAILED';
}

interface CommissionStats {
  totalCommissions: number;
  totalAmount: number;
  averageCommission: number;
  topEarners: Array<{
    userId: string;
    user: {
      email: string;
      firstName: string;
      lastName: string;
    };
    totalEarned: number;
    commissionCount: number;
  }>;
  recentActivity: number;
}

export const ReferralCommissionTracking: React.FC = () => {
  const [commissions, setCommissions] = useState<ReferralCommission[]>([]);
  const [stats, setStats] = useState<CommissionStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'DIRECT_REFERRAL' | 'MINING_PURCHASE'>('all');
  const [filterStatus, setFilterStatus] = useState<'all' | 'COMPLETED' | 'PENDING' | 'FAILED'>('all');
  const [dateRange, setDateRange] = useState<'7d' | '30d' | '90d' | 'all'>('30d');
  const [selectedUser, setSelectedUser] = useState<string | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const itemsPerPage = 20;

  useEffect(() => {
    fetchCommissions();
    fetchStats();
  }, [dateRange, filterType, filterStatus, currentPage]);

  const fetchCommissions = async () => {
    try {
      const params = new URLSearchParams({
        dateRange,
        type: filterType,
        status: filterStatus,
        page: currentPage.toString(),
        limit: itemsPerPage.toString(),
      });

      const response = await fetch(`/api/admin/referral-commissions?${params}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setCommissions(data.data);
          if (data.pagination) {
            setTotalPages(Math.ceil(data.pagination.total / itemsPerPage));
            setTotalCount(data.pagination.total);
          }
        }
      }
    } catch (error) {
      console.error('Failed to fetch referral commissions:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const params = new URLSearchParams({
        dateRange,
      });

      const response = await fetch(`/api/admin/referral-commissions/stats?${params}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setStats(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch commission stats:', error);
    }
  };

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, filterType, filterStatus, dateRange]);

  const exportData = () => {
    const csvContent = [
      ['Date', 'From User', 'To User', 'Type', 'Original Amount', 'Commission Rate', 'Commission Amount', 'Status'].join(','),
      ...filteredCommissions.map(commission => [
        formatDateTime(commission.createdAt),
        `${commission.fromUser.firstName} ${commission.fromUser.lastName} (${commission.fromUser.email})`,
        `${commission.toUser.firstName} ${commission.toUser.lastName} (${commission.toUser.email})`,
        commission.type,
        commission.originalAmount,
        `${commission.commissionRate}%`,
        commission.amount,
        commission.status
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `referral-commissions-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-400">Total Commissions</p>
                  <p className="text-2xl font-bold text-white">{formatNumber(stats.totalCommissions)}</p>
                </div>
                <Users className="h-8 w-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-400">Total Amount</p>
                  <p className="text-2xl font-bold text-white">{formatCurrency(stats.totalAmount)}</p>
                </div>
                <DollarSign className="h-8 w-8 text-green-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-400">Average Commission</p>
                  <p className="text-2xl font-bold text-white">{formatCurrency(stats.averageCommission)}</p>
                </div>
                <TrendingUp className="h-8 w-8 text-orange-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-400">Recent Activity</p>
                  <p className="text-2xl font-bold text-white">{formatNumber(stats.recentActivity)}</p>
                </div>
                <Calendar className="h-8 w-8 text-purple-400" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Top Earners */}
      {stats?.topEarners && stats.topEarners.length > 0 && (
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-white">
              <TrendingUp className="h-5 w-5 text-orange-400" />
              Top Commission Earners
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {stats.topEarners.slice(0, 6).map((earner, index) => (
                <div key={earner.userId} className="p-4 bg-slate-700 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-slate-300">#{index + 1}</span>
                    <span className="text-lg font-bold text-green-400">
                      {formatCurrency(earner.totalEarned)}
                    </span>
                  </div>
                  <div className="text-sm text-white">
                    {earner.user.firstName} {earner.user.lastName}
                  </div>
                  <div className="text-xs text-slate-400">{earner.user.email}</div>
                  <div className="text-xs text-slate-400 mt-1">
                    {earner.commissionCount} commissions
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Content */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <Users className="h-5 w-5 text-blue-400" />
            Referral Commission Tracking
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Controls */}
          <div className="flex flex-col lg:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="text"
                  placeholder="Search by user email or name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400"
                />
              </div>
            </div>
            
            <div className="flex gap-2">
              <select
                value={dateRange}
                onChange={(e) => setDateRange(e.target.value as any)}
                className="px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm"
              >
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
                <option value="all">All time</option>
              </select>

              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value as any)}
                className="px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm"
              >
                <option value="all">All Types</option>
                <option value="DIRECT_REFERRAL">Direct Referral</option>
                <option value="MINING_PURCHASE">Mining Purchase</option>
              </select>

              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value as any)}
                className="px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm"
              >
                <option value="all">All Status</option>
                <option value="COMPLETED">Completed</option>
                <option value="PENDING">Pending</option>
                <option value="FAILED">Failed</option>
              </select>
              
              <Button
                onClick={exportData}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>

          {/* Table */}
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-slate-600">
                  <th className="text-left py-3 px-4 text-slate-300">Date</th>
                  <th className="text-left py-3 px-4 text-slate-300">From → To</th>
                  <th className="text-left py-3 px-4 text-slate-300">Type</th>
                  <th className="text-right py-3 px-4 text-slate-300">Original Amount</th>
                  <th className="text-right py-3 px-4 text-slate-300">Rate</th>
                  <th className="text-right py-3 px-4 text-slate-300">Commission</th>
                  <th className="text-center py-3 px-4 text-slate-300">Status</th>
                </tr>
              </thead>
              <tbody>
                {commissions.map((commission) => (
                  <tr key={commission.id} className="border-b border-slate-700 hover:bg-slate-700/50">
                    <td className="py-3 px-4 text-slate-300">
                      {formatDateTime(commission.createdAt)}
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <div className="text-xs">
                          <div className="text-white">
                            {commission.fromUser.firstName} {commission.fromUser.lastName}
                          </div>
                          <div className="text-slate-400">{commission.fromUser.email}</div>
                        </div>
                        <ArrowRight className="h-3 w-3 text-slate-400" />
                        <div className="text-xs">
                          <div className="text-white">
                            {commission.toUser.firstName} {commission.toUser.lastName}
                          </div>
                          <div className="text-slate-400">{commission.toUser.email}</div>
                        </div>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 rounded text-xs ${
                        commission.type === 'DIRECT_REFERRAL' 
                          ? 'bg-blue-600/20 text-blue-400' 
                          : 'bg-green-600/20 text-green-400'
                      }`}>
                        {commission.type === 'DIRECT_REFERRAL' ? 'Referral' : 'Mining'}
                      </span>
                    </td>
                    <td className="text-right py-3 px-4 text-white">
                      {formatCurrency(commission.originalAmount)}
                    </td>
                    <td className="text-right py-3 px-4 text-slate-300">
                      {commission.commissionRate}%
                    </td>
                    <td className="text-right py-3 px-4 text-green-400 font-medium">
                      {formatCurrency(commission.amount)}
                    </td>
                    <td className="text-center py-3 px-4">
                      <span className={`px-2 py-1 rounded text-xs ${
                        commission.status === 'COMPLETED' 
                          ? 'bg-green-600/20 text-green-400' 
                          : commission.status === 'PENDING'
                          ? 'bg-yellow-600/20 text-yellow-400'
                          : 'bg-red-600/20 text-red-400'
                      }`}>
                        {commission.status}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {commissions.length === 0 && !loading && (
            <div className="text-center py-8 text-slate-400">
              No referral commissions found matching your criteria.
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6 pt-4 border-t border-slate-600">
              <div className="text-sm text-slate-400">
                Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, totalCount)} of {totalCount} results
              </div>
              <div className="flex items-center gap-2">
                <Button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="bg-slate-700 hover:bg-slate-600 text-white border-slate-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>

                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
                    if (pageNum > totalPages) return null;

                    return (
                      <Button
                        key={pageNum}
                        onClick={() => setCurrentPage(pageNum)}
                        className={`w-10 h-10 ${
                          currentPage === pageNum
                            ? 'bg-blue-600 hover:bg-blue-700 text-white'
                            : 'bg-slate-700 hover:bg-slate-600 text-slate-300 border-slate-600'
                        }`}
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </div>

                <Button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="bg-slate-700 hover:bg-slate-600 text-white border-slate-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
